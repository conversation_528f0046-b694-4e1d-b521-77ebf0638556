<script setup lang="ts">
import type { TransformToVoQuestionData } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import type { FormInst } from 'naive-ui'
import QuestionItemContainer from '@sa/components/common/questions/question-item-container.vue'

defineOptions({
  name: 'EditQuestionDrawer',
})

// 定义props
const props = defineProps<{
  question: TransformToVoQuestionData
}>()

// 定义emits
const emit = defineEmits<{
  save: [question: TransformToVoQuestionData]
  cancel: []
}>()

// 抽屉显示状态
const visible = defineModel<boolean>({
  default: false,
})

// 监听抽屉关闭
function handleClose() {
  visible.value = false
  emit('cancel')
}

// 保存题目
function handleSave() {
  if (props.question) {
    emit('save', props.question)
    visible.value = false
  }
}
const formRef = ref<FormInst | null>(null)

const dynamicForm = reactive({
  name: '',
  hobbies: [{ hobby: '' }],
})
// 创建本地副本用于编辑
const localQuestion = ref<TransformToVoQuestionData>()

// 监听 props.question 变化，同步到本地副本
watch(() => props.question, (newQuestion) => {
  if (newQuestion) {
    localQuestion.value = { ...newQuestion }
  }
}, { immediate: true, deep: true })
</script>

<template>
  <NDrawer v-model:show="visible" :width="800" placement="right">
    <NDrawerContent :title="`编辑-${localQuestion!.typeText}`" closable :native-scrollbar="false" @close="handleClose">
      <NForm ref="formRef" :model="dynamicForm" label-placement="left" label-width="70px">
        <NFormItem label="题干" :rules="[{ required: true, message: '请输入姓名' }]">
          <CKEditor />
        </NFormItem>
        <QuestionItemContainer :item-info="question" type="edit" :show-question-stem="false" />
        <NFormItem label="答案解析" :rules="[{ required: true, message: '请输入答案解析' }]">
          <CKEditor />
        </NFormItem>
      </NForm>

      <!-- <div class="test w-500px">
        <CKEditor v-for="item in 10" :key="item" class="mb-20px" />
      </div> -->
      <!-- 抽屉底部操作按钮 -->
      <template #footer>
        <div class="flex justify-end gap-3">
          <NButton @click="handleClose">
            取消
          </NButton>
          <NButton type="primary" @click="handleSave">
            保存
          </NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
